import { Module, Global } from '@nestjs/common';
import { QueueService } from './queue.service';
import { QueueHealthService } from './queue-health.service';
import { BullBoardService } from './bullboard.service';

/**
 * Global Queue Module for BullMQ integration
 * Provides queue infrastructure and services across the application
 */
@Global()
@Module({
  providers: [
    QueueService,
    QueueHealthService,
    BullBoardService,
  ],
  exports: [
    QueueService,
    QueueHealthService,
    BullBoardService,
  ],
})
export class QueueModule {}
