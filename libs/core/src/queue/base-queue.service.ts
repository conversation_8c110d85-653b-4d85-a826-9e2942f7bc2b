import { Injectable, Logger, OnModuleD<PERSON>roy } from '@nestjs/common';
import { Queue, Job, JobsOptions, RepeatOptions } from 'bullmq';
import { Redis } from 'ioredis';
import { z } from 'zod/v4';
import { DateTimeUtilsService } from '@app/utils';
import { QueueError, type QueueHealthStatus, type JobResult } from './queue.error';
import { 
  type QueueConnectionType, 
  type QueueOptionsType, 
  type JobDataType,
  JobDataSchema,
  QUEUE_PRIORITY_MAP,
  type QueuePriorityType 
} from './queue.config';

/**
 * Abstract base class for queue services
 * Provides common queue operations and standardized error handling
 */
@Injectable()
export abstract class BaseQueueService<T extends Record<string, unknown> = Record<string, unknown>> 
  implements OnModuleDestroy {
  
  protected readonly logger = new Logger(this.constructor.name);
  protected queue: Queue<T>;
  protected connection: Redis;
  
  constructor(
    protected readonly queueName: string,
    protected readonly connectionConfig: QueueConnectionType,
    protected readonly queueOptions: QueueOptionsType,
    protected readonly dateTimeUtils: DateTimeUtilsService,
  ) {
    this.initializeQueue();
  }

  /**
   * Initialize the queue with connection and options
   */
  private initializeQueue(): void {
    try {
      // Create Redis connection for BullMQ
      this.connection = new Redis({
        host: this.connectionConfig.host,
        port: this.connectionConfig.port,
        username: this.connectionConfig.username,
        password: this.connectionConfig.password,
        db: this.connectionConfig.db,
        maxRetriesPerRequest: this.connectionConfig.maxRetriesPerRequest,
        retryDelayOnFailover: this.connectionConfig.retryDelayOnFailover,
        enableReadyCheck: this.connectionConfig.enableReadyCheck,
        lazyConnect: this.connectionConfig.lazyConnect,
        keepAlive: this.connectionConfig.keepAlive,
        family: this.connectionConfig.family,
        keyPrefix: this.connectionConfig.keyPrefix,
      });

      // Create BullMQ queue
      this.queue = new Queue<T>(this.queueName, {
        connection: this.connection,
        defaultJobOptions: this.queueOptions.defaultJobOptions,
        settings: this.queueOptions.settings,
      });

      this.logger.log(`Queue '${this.queueName}' initialized successfully`);
    } catch (error) {
      this.logger.error(`Failed to initialize queue '${this.queueName}':`, error);
      throw new QueueError('QUEUE_CREATION_FAILED', {
        message: `Failed to initialize queue '${this.queueName}'`,
        cause: error,
        context: { queueName: this.queueName },
      });
    }
  }

  /**
   * Add a job to the queue
   */
  async addJob(
    jobName: string, 
    data: T, 
    options?: Partial<JobsOptions>
  ): Promise<Job<T>> {
    try {
      // Validate job data
      const validatedData = this.validateJobData({ data, opts: options || {} });
      
      const job = await this.queue.add(jobName, data, {
        ...this.queueOptions.defaultJobOptions,
        ...options,
      });

      this.logger.log(`Job '${jobName}' added to queue '${this.queueName}' with ID: ${job.id}`);
      return job;
    } catch (error) {
      this.logger.error(`Failed to add job '${jobName}' to queue '${this.queueName}':`, error);
      throw new QueueError('JOB_ADD_FAILED', {
        message: `Failed to add job '${jobName}' to queue '${this.queueName}'`,
        cause: error,
        context: { queueName: this.queueName, jobName, data },
      });
    }
  }

  /**
   * Add a job with priority
   */
  async addJobWithPriority(
    jobName: string,
    data: T,
    priority: QueuePriorityType,
    options?: Partial<JobsOptions>
  ): Promise<Job<T>> {
    const priorityValue = QUEUE_PRIORITY_MAP[priority];
    return this.addJob(jobName, data, { ...options, priority: priorityValue });
  }

  /**
   * Add a delayed job
   */
  async addDelayedJob(
    jobName: string,
    data: T,
    delayMs: number,
    options?: Partial<JobsOptions>
  ): Promise<Job<T>> {
    return this.addJob(jobName, data, { ...options, delay: delayMs });
  }

  /**
   * Add a repeating job
   */
  async addRepeatingJob(
    jobName: string,
    data: T,
    repeatOptions: RepeatOptions,
    options?: Partial<JobsOptions>
  ): Promise<Job<T>> {
    return this.addJob(jobName, data, { ...options, repeat: repeatOptions });
  }

  /**
   * Get a job by ID
   */
  async getJob(jobId: string): Promise<Job<T> | undefined> {
    try {
      return await this.queue.getJob(jobId);
    } catch (error) {
      this.logger.error(`Failed to get job '${jobId}' from queue '${this.queueName}':`, error);
      throw new QueueError('JOB_GET_FAILED', {
        message: `Failed to get job '${jobId}' from queue '${this.queueName}'`,
        cause: error,
        context: { queueName: this.queueName, jobId },
      });
    }
  }

  /**
   * Remove a job by ID
   */
  async removeJob(jobId: string): Promise<void> {
    try {
      const job = await this.getJob(jobId);
      if (job) {
        await job.remove();
        this.logger.log(`Job '${jobId}' removed from queue '${this.queueName}'`);
      }
    } catch (error) {
      this.logger.error(`Failed to remove job '${jobId}' from queue '${this.queueName}':`, error);
      throw new QueueError('JOB_REMOVE_FAILED', {
        message: `Failed to remove job '${jobId}' from queue '${this.queueName}'`,
        cause: error,
        context: { queueName: this.queueName, jobId },
      });
    }
  }

  /**
   * Pause the queue
   */
  async pauseQueue(): Promise<void> {
    try {
      await this.queue.pause();
      this.logger.log(`Queue '${this.queueName}' paused`);
    } catch (error) {
      this.logger.error(`Failed to pause queue '${this.queueName}':`, error);
      throw new QueueError('QUEUE_PAUSE_FAILED', {
        message: `Failed to pause queue '${this.queueName}'`,
        cause: error,
        context: { queueName: this.queueName },
      });
    }
  }

  /**
   * Resume the queue
   */
  async resumeQueue(): Promise<void> {
    try {
      await this.queue.resume();
      this.logger.log(`Queue '${this.queueName}' resumed`);
    } catch (error) {
      this.logger.error(`Failed to resume queue '${this.queueName}':`, error);
      throw new QueueError('QUEUE_RESUME_FAILED', {
        message: `Failed to resume queue '${this.queueName}'`,
        cause: error,
        context: { queueName: this.queueName },
      });
    }
  }

  /**
   * Get queue health status
   */
  async getHealthStatus(): Promise<QueueHealthStatus> {
    try {
      const [waiting, active, completed, failed, delayed] = await Promise.all([
        this.queue.getWaiting(),
        this.queue.getActive(),
        this.queue.getCompleted(),
        this.queue.getFailed(),
        this.queue.getDelayed(),
      ]);

      const isPaused = await this.queue.isPaused();

      return {
        name: this.queueName,
        isHealthy: true,
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
        paused: isPaused,
      };
    } catch (error) {
      this.logger.error(`Failed to get health status for queue '${this.queueName}':`, error);
      return {
        name: this.queueName,
        isHealthy: false,
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        delayed: 0,
        paused: false,
        lastError: error instanceof Error ? error.message : 'Unknown error',
        lastErrorAt: this.dateTimeUtils.getCurrentUtcDateTime(),
      };
    }
  }

  /**
   * Clean completed and failed jobs
   */
  async cleanQueue(olderThanMs: number = 24 * 60 * 60 * 1000): Promise<void> {
    try {
      await Promise.all([
        this.queue.clean(olderThanMs, 100, 'completed'),
        this.queue.clean(olderThanMs, 50, 'failed'),
      ]);
      this.logger.log(`Queue '${this.queueName}' cleaned successfully`);
    } catch (error) {
      this.logger.error(`Failed to clean queue '${this.queueName}':`, error);
      throw new QueueError('QUEUE_CLEAN_FAILED', {
        message: `Failed to clean queue '${this.queueName}'`,
        cause: error,
        context: { queueName: this.queueName },
      });
    }
  }

  /**
   * Validate job data using Zod schema
   */
  private validateJobData(data: unknown): JobDataType {
    try {
      return JobDataSchema.parse(data);
    } catch (error) {
      throw new QueueError('INVALID_JOB_DATA', {
        message: 'Invalid job data provided',
        cause: error,
        context: { queueName: this.queueName, data },
      });
    }
  }

  /**
   * Get the queue instance (for advanced operations)
   */
  getQueue(): Queue<T> {
    return this.queue;
  }

  /**
   * Cleanup resources on module destroy
   */
  async onModuleDestroy(): Promise<void> {
    try {
      await this.queue.close();
      await this.connection.disconnect();
      this.logger.log(`Queue '${this.queueName}' closed successfully`);
    } catch (error) {
      this.logger.error(`Error closing queue '${this.queueName}':`, error);
    }
  }
}
